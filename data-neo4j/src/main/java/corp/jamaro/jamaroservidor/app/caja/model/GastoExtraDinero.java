package corp.jamaro.jamaroservidor.app.caja.model;

import corp.jamaro.jamaroservidor.app.dinero.model.Dinero;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
@Node
public class GastoExtraDinero {

    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private String motivo;
    private Double monto;
    private String realizadoPor;//username que realizo el gasto

    private Instant realizadoEl = Instant.now();

    @Relationship(type = "CON_DINERO_GASTADO")//dinero del tipo esEntrada false
    private Set<Dinero> dineroGastado;
}
