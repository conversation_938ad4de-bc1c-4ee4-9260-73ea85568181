package corp.jamaro.jamaroservidor.app.ventas.service;

import corp.jamaro.jamaroservidor.app.repository.ClienteRepository;
import corp.jamaro.jamaroservidor.app.model.User;
import corp.jamaro.jamaroservidor.app.producto.repository.ItemRepository;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import corp.jamaro.jamaroservidor.app.ventas.model.BienServicioCargado;
import corp.jamaro.jamaroservidor.app.ventas.model.BienServicioDevuelto;
import corp.jamaro.jamaroservidor.app.ventas.repository.SaleRepository;
import corp.jamaro.jamaroservidor.app.ventas.repository.BienServicioCargadoRepository;
import corp.jamaro.jamaroservidor.app.ventas.repository.BienServicioDevueltoRepository;
import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramado;
import corp.jamaro.jamaroservidor.app.caja.model.DevolucionDinero;
import corp.jamaro.jamaroservidor.app.dinero.repository.CobroDineroProgramadoRepository;
import corp.jamaro.jamaroservidor.app.dinero.repository.DevolucionDineroRepository;
import corp.jamaro.jamaroservidor.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.Instant;
import java.util.UUID;

/**
 * Servicio para gestionar las operaciones relacionadas con las ventas (Sale).
 * Este servicio implementa el patrón de comunicación RSocket donde:
 * 1. El cliente envía solicitudes de actualización
 * 2. El servidor procesa las solicitudes y devuelve un resultado de éxito/error
 * 3. El cliente recibe las actualizaciones reales a través de su suscripción
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SaleService {

    private final SaleRepository saleRepository;
    private final ClienteRepository clienteRepository;
    private final ItemRepository itemRepository;
    private final BienServicioCargadoRepository bienServicioCargadoRepository;
    private final BienServicioDevueltoRepository bienServicioDevueltoRepository;
    private final CobroDineroProgramadoRepository cobroDineroProgramadoRepository;
    private final DevolucionDineroRepository devolucionDineroRepository;

    // TransactionalOperator autoconfigurado por Spring Boot para operaciones críticas que tienen que ser ejecutadas dentro de una transacción
    private final TransactionalOperator transactionalOperator;

    // Emisor para notificar actualizaciones vía RSocket
    // Utiliza multicast.directBestEffort() para enviar actualizaciones a múltiples suscriptores
    // sin bloquear si algún suscriptor es lento
    private final Sinks.Many<Sale> updateSink = Sinks.many().multicast().directBestEffort();

    /**
     * Permite a los clientes suscribirse a actualizaciones de un Sale específico.
     *
     * El flujo emitirá:
     * 1. El estado actual del Sale como primer elemento
     * 2. Todas las actualizaciones futuras que se realicen sobre ese Sale
     *
     * @param saleId ID del Sale al que se quiere suscribir
     * @return Flux que emite el Sale actual y sus actualizaciones futuras
     */
    public Flux<Sale> subscribeToSaleUpdates(UUID saleId) {
        log.info("Suscribiendo a actualizaciones del Sale con ID: {}", saleId);

        // Primero obtenemos el estado actual del Sale con todas sus relaciones
        Mono<Sale> currentState = saleRepository.findById(saleId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Sale no encontrado con ID: " + saleId)));

        // Luego nos suscribimos al flujo de actualizaciones, filtrando solo las del Sale solicitado
        Flux<Sale> updates = updateSink.asFlux()
                .filter(sale -> sale.getId().equals(saleId));

        // Concatenamos el estado actual con las actualizaciones futuras
        return currentState.concatWith(updates);
    }


    /**
     * Emite el estado actual del Sale con todas sus relaciones.
     *
     * @param saleId ID del Sale a emitir
     * @return Mono<Void> que completa cuando la operación termina
     */
    private Mono<Void> emitSale(UUID saleId) {
        return saleRepository.findById(saleId)
                .doOnNext(freshSale -> {
                    log.debug("Emitiendo actualización para Sale con ID: {}", saleId);
                    updateSink.tryEmitNext(freshSale);
                })
                .then();
    }



    /**
     * Actualiza el Cliente de un Sale.
     * Si clienteId es null, elimina la relación con Cliente (venta genérica).
     *
     * @param saleId ID del Sale a actualizar
     * @param clienteId UUID del Cliente a asignar, o null para venta genérica
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<Boolean> updateCliente(UUID saleId, UUID clienteId) {
        log.debug("Actualizando Cliente en Sale con ID: {}, clienteId: {}", saleId, clienteId);

        return saleRepository.existsById(saleId)
                .flatMap(exists -> {
                    if (!exists) {
                        return Mono.error(new IllegalArgumentException("Sale no encontrado con ID: " + saleId));
                    }

                    // Si clienteId es null, no validamos existencia del cliente
                    if (clienteId == null) {
                        return Mono.just(true);
                    }

                    return clienteRepository.existsById(clienteId);
                })
                .flatMap(exists -> {
                    if (clienteId != null && !exists) {
                        return Mono.error(new IllegalArgumentException("Cliente no encontrado con ID: " + clienteId));
                    }
                    return saleRepository.updateCliente(saleId, clienteId);
                })
                .flatMap(id -> emitSale(saleId).thenReturn(true))
                .onErrorResume(e -> {
                    log.error("Error al actualizar Cliente en Sale: {}", e.getMessage());
                    return Mono.just(false);
                });
    }


    /**
     * Agrega un Item a un Sale como BienServicioCargado o actualiza la cantidad si ya existe.
     *
     * Lógica de negocio:
     * 1. Si ya existe BienServicioCargado para el Item → incrementa cantidad (ignora precioInicial)
     * 2. Si no existe → crea nuevo BienServicioCargado
     * 3. Si precioInicial es null → usa precioVentaPublico del Item
     * 4. Recalcula totales del Sale automáticamente
     *
     * OPERACIÓN TRANSACCIONAL: Usa TransactionalOperator para garantizar consistencia.
     * Si falla cualquier paso, toda la operación se revierte automáticamente.
     *
     * @param saleId ID del Sale a actualizar
     * @param itemCodCompuesto Código compuesto del Item a agregar
     * @param precioInicial Precio inicial unitario (si es null usa precioVentaPublico del Item)
     * @param cantidad Cantidad a agregar (si es null usa 1.0)
     * @return Mono<Boolean> true si la operación fue exitosa, false en caso de error
     */
    public Mono<Boolean> addItemToSale(UUID saleId, String itemCodCompuesto, Double precioInicial, Double cantidad) {
        log.debug("Agregando Item a Sale con ID: {}, itemCodCompuesto: {}", saleId, itemCodCompuesto);

        // Aplicar valores por defecto
        Double finalCantidad = (cantidad != null) ? cantidad : 1.0;

        // Validaciones básicas fuera de la transacción
        return validateSaleAndItemExist(saleId, itemCodCompuesto)
                .flatMap(valid -> SecurityUtils.getCurrentUser().map(User::getUsername))
                .flatMap(username -> {
                    // OPERACIÓN TRANSACCIONAL: Todas las operaciones de modificación dentro de la transacción
                    return saleRepository.existsBienServicioCargadoForItem(saleId, itemCodCompuesto)
                        .flatMap(exists -> {
                            if (exists) {
                                // Caso 1: Ya existe → incrementar cantidad
                                return incrementExistingBienServicioCargado(saleId, itemCodCompuesto, finalCantidad)
                                        .flatMap(id -> saleRepository.calculateAndUpdateTotals(saleId));
                            } else {
                                // Caso 2: No existe → crear nuevo BienServicioCargado con todas sus relaciones
                                return createNewBienServicioCargado(saleId, itemCodCompuesto, precioInicial, finalCantidad, username)
                                        .flatMap(id -> saleRepository.calculateAndUpdateTotals(saleId));
                            }
                        })
                        .as(transactionalOperator::transactional);
                })
                .flatMap(sale -> emitSale(saleId).thenReturn(true))
                .onErrorResume(e -> {
                    log.error("Error al agregar Item a Sale: {}", e.getMessage());
                    return Mono.just(false);
                });
    }



    /**
     * Actualiza los campos de un BienServicioCargado.
     * Recalcula montoAcordado = precioAcordado * cantidad y actualiza totales del Sale.
     *
     * OPERACIÓN TRANSACCIONAL: Usa TransactionalOperator para garantizar consistencia.
     * Si falla cualquier paso, toda la operación se revierte automáticamente.
     *
     * @param saleId ID del Sale a actualizar
     * @param bienServicioCargadoId ID del BienServicioCargado a actualizar
     * @param precioAcordado Nuevo precio acordado unitario
     * @param cantidad Nueva cantidad (opcional, si es null no se actualiza)
     * @param descripcionDelBienServicio Nueva descripción (opcional, si es null no se actualiza)
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<Boolean> updateBienServicioCargado(UUID saleId, UUID bienServicioCargadoId, Double precioAcordado, Double cantidad, String descripcionDelBienServicio) {
        log.debug("Actualizando BienServicioCargado en Sale con ID: {}, bienServicioCargadoId: {}, precioAcordado: {}, cantidad: {}, descripcion: {}",
                 saleId, bienServicioCargadoId, precioAcordado, cantidad, descripcionDelBienServicio);

        // OPERACIÓN TRANSACCIONAL: Actualización y recálculo dentro de la transacción
        return saleRepository.updateBienServicioCargado(saleId, bienServicioCargadoId, precioAcordado, cantidad, descripcionDelBienServicio)
                .flatMap(id -> saleRepository.calculateAndUpdateTotals(saleId))
                .as(transactionalOperator::transactional)
                .flatMap(sale -> emitSale(saleId).thenReturn(true))
        .onErrorResume(e -> {
            log.error("Error al actualizar BienServicioCargado: {}", e.getMessage());
            return Mono.just(false);
        });
    }

    /**
     * Elimina un BienServicioCargado y actualiza totalMontoInicial, totalMontoAcordado.
     *
     * OPERACIÓN TRANSACCIONAL: Usa TransactionalOperator para garantizar consistencia.
     * Si falla cualquier paso, toda la operación se revierte automáticamente.
     *
     * @param saleId ID del Sale a actualizar
     * @param bienServicioCargadoId ID del BienServicioCargado a eliminar
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<Boolean> deleteBienServicioCargado(UUID saleId, UUID bienServicioCargadoId) {
        log.debug("Eliminando BienServicioCargado de Sale con ID: {}, bienServicioCargadoId: {}", saleId, bienServicioCargadoId);

        // OPERACIÓN TRANSACCIONAL: Eliminación y recálculo dentro de la transacción
        return saleRepository.deleteBienServicioCargado(saleId, bienServicioCargadoId)
                .flatMap(id -> saleRepository.calculateAndUpdateTotals(saleId))
                .as(transactionalOperator::transactional)
                .flatMap(sale -> emitSale(saleId).thenReturn(true))
        .onErrorResume(e -> {
            log.error("Error al eliminar BienServicioCargado: {}", e.getMessage());
            return Mono.just(false);
        });
    }

    /**
     * Elimina todos los nodos BienServicioCargado de un Sale y actualiza totalMontoInicial, totalMontoAcordado.
     *
     * OPERACIÓN TRANSACCIONAL: Usa TransactionalOperator para garantizar consistencia.
     * Si falla cualquier paso, toda la operación se revierte automáticamente.
     *
     * Usa repositorios específicos para trabajar con objetos y evitar problemas reactivos.
     *
     * @param saleId ID del Sale a actualizar
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<Boolean> deleteAllBienServicioCargado(UUID saleId) {
        log.debug("Eliminando todos los BienServicioCargado de Sale con ID: {}", saleId);

        // Validación fuera de la transacción
        return saleRepository.existsById(saleId)
                .flatMap(exists -> {
                    if (!exists) {
                        return Mono.error(new IllegalArgumentException("Sale no encontrado con ID: " + saleId));
                    }

                    // OPERACIÓN TRANSACCIONAL: Eliminación usando repositorios específicos y recálculo
                    return getAllBienServicioCargadoFromSale(saleId)
                            .collectList()
                            .flatMap(bienServiciosList -> {
                                if (bienServiciosList.isEmpty()) {
                                    log.debug("No hay BienServicioCargado para eliminar en Sale: {}", saleId);
                                    // Solo recalcular totales para asegurar consistencia
                                    return saleRepository.calculateAndUpdateTotals(saleId);
                                } else {
                                    log.debug("Eliminando {} BienServicioCargado del Sale: {}", bienServiciosList.size(), saleId);
                                    // Eliminar todos usando repositorio específico
                                    return bienServicioCargadoRepository.deleteAll(bienServiciosList)
                                            .then(saleRepository.calculateAndUpdateTotals(saleId));
                                }
                            });
                })
                .as(transactionalOperator::transactional)
                .flatMap(sale -> emitSale(saleId).thenReturn(true))
                .onErrorResume(e -> {
                    log.error("Error al eliminar todos los BienServicioCargado: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

    /**
     * Inicia una venta de contado.
     *
     * Lógica de negocio:
     * 1. Verifica que exista al menos un BienServicioCargado
     * 2. Recalcula totales del Sale
     * 3. Actualiza campos del Sale (tipoVenta=CONTADO, estaPagadoEntregado=false, totalRestante=totalMontoAcordado)
     * 4. Crea CobroDineroProgramado con límite de 30 minutos
     * 5. Crea relación Sale-CobroDineroProgramado
     *
     * OPERACIÓN TRANSACCIONAL: Usa TransactionalOperator para garantizar consistencia.
     * Si falla cualquier paso, toda la operación se revierte automáticamente.
     *
     * @param saleId ID del Sale a iniciar como venta de contado
     * @return Mono<Boolean> true si la operación fue exitosa, false en caso de error
     */
    public Mono<Boolean> iniciarVentaContado(UUID saleId) {
        log.debug("Iniciando venta de contado para Sale con ID: {}", saleId);

        // Validaciones fuera de la transacción
        return validateSaleHasBienServicioCargado(saleId)
                .flatMap(valid -> SecurityUtils.getCurrentUser().map(User::getUsername))
                .flatMap(username -> {
                    // OPERACIÓN TRANSACCIONAL: Todas las operaciones de modificación dentro de la transacción
                    return saleRepository.calculateAndUpdateTotals(saleId)
                            .flatMap(sale -> {
                                // Paso 2: Crear CobroDineroProgramado usando repositorio específico
                                CobroDineroProgramado cobroProgramado = createCobroDineroProgramado(
                                    sale.getTotalMontoAcordado(), username);

                                return cobroDineroProgramadoRepository.save(cobroProgramado)
                                        .flatMap(savedCobro -> {
                                            // Paso 3: Crear relación Sale-CobroDineroProgramado
                                            return saleRepository.createSaleCobroDineroProgramadoRelationship(saleId, savedCobro.getId())
                                                    .flatMap(id -> {
                                                        // Paso 4: Actualizar campos del Sale para venta de contado
                                                        return saleRepository.updateSaleForContado(saleId);
                                                    });
                                        });
                            })
                            .as(transactionalOperator::transactional);
                })
                .flatMap(sale -> emitSale(saleId).thenReturn(true))
                .onErrorResume(e -> {
                    log.error("Error al iniciar venta de contado: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

    /**
     * Inicia una venta a crédito.
     *
     * Lógica de negocio:
     * 1. Verifica que exista al menos un BienServicioCargado
     * 2. Recalcula totales del Sale
     * 3. Actualiza campos del Sale (tipoVenta=CREDITO, estaPagadoEntregado=false, totalRestante=totalMontoAcordado)
     * 4. Crea CobroDineroProgramado con límite de 1 semana
     * 5. Crea relación Sale-CobroDineroProgramado
     *
     * OPERACIÓN TRANSACCIONAL: Usa TransactionalOperator para garantizar consistencia.
     * Si falla cualquier paso, toda la operación se revierte automáticamente.
     *
     * @param saleId ID del Sale a iniciar como venta a crédito
     * @return Mono<Boolean> true si la operación fue exitosa, false en caso de error
     */
    public Mono<Boolean> iniciarVentaCredito(UUID saleId) {
        log.debug("Iniciando venta a crédito para Sale con ID: {}", saleId);

        // Validaciones fuera de la transacción
        return validateSaleHasBienServicioCargado(saleId)
                .flatMap(valid -> SecurityUtils.getCurrentUser().map(User::getUsername))
                .flatMap(username -> {
                    // OPERACIÓN TRANSACCIONAL: Todas las operaciones de modificación dentro de la transacción
                    return saleRepository.calculateAndUpdateTotals(saleId)
                            .flatMap(sale -> {
                                // Paso 2: Crear CobroDineroProgramado con límite de 1 semana usando repositorio específico
                                CobroDineroProgramado cobroProgramado = createCobroDineroProgramadoWithWeekLimit(
                                    sale.getTotalMontoAcordado(), username);

                                return cobroDineroProgramadoRepository.save(cobroProgramado)
                                        .flatMap(savedCobro -> {
                                            // Paso 3: Crear relación Sale-CobroDineroProgramado
                                            return saleRepository.createSaleCobroDineroProgramadoRelationship(saleId, savedCobro.getId())
                                                    .flatMap(id -> {
                                                        // Paso 4: Actualizar campos del Sale para venta a crédito
                                                        return saleRepository.updateSaleForCredito(saleId);
                                                    });
                                        });
                            })
                            .as(transactionalOperator::transactional);
                })
                .flatMap(sale -> emitSale(saleId).thenReturn(true))
                .onErrorResume(e -> {
                    log.error("Error al iniciar venta a crédito: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

    /**
     * Inicia una venta como pedido.
     *
     * Lógica de negocio:
     * 1. Verifica que exista al menos un BienServicioCargado
     * 2. Recalcula totales del Sale
     * 3. Actualiza campos del Sale (tipoVenta=PEDIDO, estaPagadoEntregado=false, totalRestante=totalMontoAcordado)
     * 4. Crea CobroDineroProgramado con límite de 1 semana
     * 5. Crea relación Sale-CobroDineroProgramado
     *
     * OPERACIÓN TRANSACCIONAL: Usa TransactionalOperator para garantizar consistencia.
     * Si falla cualquier paso, toda la operación se revierte automáticamente.
     *
     * @param saleId ID del Sale a iniciar como pedido
     * @return Mono<Boolean> true si la operación fue exitosa, false en caso de error
     */
    public Mono<Boolean> iniciarVentaPedido(UUID saleId) {
        log.debug("Iniciando venta como pedido para Sale con ID: {}", saleId);

        // Validaciones fuera de la transacción
        return validateSaleHasBienServicioCargado(saleId)
                .flatMap(valid -> SecurityUtils.getCurrentUser().map(User::getUsername))
                .flatMap(username -> {
                    // OPERACIÓN TRANSACCIONAL: Todas las operaciones de modificación dentro de la transacción
                    return saleRepository.calculateAndUpdateTotals(saleId)
                            .flatMap(sale -> {
                                // Paso 2: Crear CobroDineroProgramado con límite de 1 semana usando repositorio específico
                                CobroDineroProgramado cobroProgramado = createCobroDineroProgramadoWithWeekLimit(
                                    sale.getTotalMontoAcordado(), username);

                                return cobroDineroProgramadoRepository.save(cobroProgramado)
                                        .flatMap(savedCobro -> {
                                            // Paso 3: Crear relación Sale-CobroDineroProgramado
                                            return saleRepository.createSaleCobroDineroProgramadoRelationship(saleId, savedCobro.getId())
                                                    .flatMap(id -> {
                                                        // Paso 4: Actualizar campos del Sale para venta como pedido
                                                        return saleRepository.updateSaleForPedido(saleId);
                                                    });
                                        });
                            })
                            .as(transactionalOperator::transactional);
                })
                .flatMap(sale -> emitSale(saleId).thenReturn(true))
                .onErrorResume(e -> {
                    log.error("Error al iniciar venta como pedido: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

    /**
     * Inicia un BienServicioDevuelto modificando el BienServicioCargado correspondiente y creando un DevolucionDinero.
     *
     * Lógica de negocio:
     * 1. Valida que cantidad y precioAcordadoDevolver estén presentes y cantidad > 0
     * 2. Valida que cantidad a devolver <= cantidad del BienServicioCargado
     * 3. Completa campos del BienServicioDevuelto (devueltoPor, descripción, montoDevuelto, etc.)
     * 4. Guarda BienServicioDevuelto y crea relaciones con Sale e Item
     * 5. Modifica BienServicioCargado: reduce cantidad o lo elimina si cantidad devuelta = cantidad cargada
     * 6. Crea DevolucionDinero con montoADevolver = BienServicioDevuelto.montoDevuelto
     * 7. Recalcula totales del Sale
     *
     * OPERACIÓN TRANSACCIONAL: Usa TransactionalOperator para garantizar consistencia.
     * Si falla cualquier paso, toda la operación se revierte automáticamente.
     *
     * @param saleId ID del Sale
     * @param bienServicioCargadoId ID del BienServicioCargado a devolver
     * @param bienServicioDevuelto BienServicioDevuelto parcialmente inicializado (requiere cantidad y precioAcordadoDevolver)
     * @return Mono<Boolean> true si la operación fue exitosa, false en caso de error
     */
    public Mono<Boolean> iniciarBienServicioDevuelto(UUID saleId, UUID bienServicioCargadoId, BienServicioDevuelto bienServicioDevuelto) {
        log.debug("Iniciando BienServicioDevuelto para Sale con ID: {}, bienServicioCargadoId: {}", saleId, bienServicioCargadoId);

        // Validaciones de entrada fuera de la transacción
        return validateBienServicioDevueltoInput(bienServicioDevuelto)
                .flatMap(valid -> validateSaleExists(saleId))
                .flatMap(valid -> {
                    // Obtener BienServicioCargado usando repositorio específico
                    return bienServicioCargadoRepository.findById(bienServicioCargadoId)
                            .switchIfEmpty(Mono.error(new IllegalArgumentException("BienServicioCargado no encontrado con ID: " + bienServicioCargadoId)))
                            .flatMap(bienServicioCargado -> {
                                // Validar cantidad a devolver vs cantidad cargada
                                return validateCantidadDevolucion(bienServicioDevuelto.getCantidad(), bienServicioCargado.getCantidad())
                                        .flatMap(valid2 -> SecurityUtils.getCurrentUser().map(User::getUsername))
                                        .flatMap(username -> {
                                            // Completar BienServicioDevuelto usando datos del BienServicioCargado
                                            completeBienServicioDevuelto(bienServicioDevuelto, bienServicioCargado, username);

                                            // OPERACIÓN TRANSACCIONAL: Todas las operaciones de modificación dentro de la transacción
                                            return bienServicioDevueltoRepository.save(bienServicioDevuelto)
                                                    .flatMap(savedBsd -> {
                                                        // Crear relaciones y modificar BienServicioCargado
                                                        return processDevolucionRelationshipsAndModifications(
                                                            saleId, bienServicioCargadoId, savedBsd, bienServicioCargado, username)
                                                                .flatMap(id -> saleRepository.calculateAndUpdateTotals(saleId));
                                                    })
                                                    .as(transactionalOperator::transactional);
                                        });
                            });
                })
                .flatMap(sale -> emitSale(saleId).thenReturn(true))
                .onErrorResume(e -> {
                    log.error("Error al iniciar BienServicioDevuelto: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

    // ========================================
    // MÉTODOS AUXILIARES PRIVADOS
    // ========================================

    /**
     * Obtiene todos los BienServicioCargado de un Sale específico.
     * Usa consulta específica para obtener solo los IDs y luego cargar las entidades.
     */
    private Flux<BienServicioCargado> getAllBienServicioCargadoFromSale(UUID saleId) {
        return saleRepository.getBienServicioCargadoIdsBySaleId(saleId)
                .flatMap(bienServicioCargadoRepository::findById);
    }

    /**
     * Valida que el Sale y el Item existan.
     * Método auxiliar para reducir duplicación de código.
     */
    private Mono<Boolean> validateSaleAndItemExist(UUID saleId, String itemCodCompuesto) {
        return saleRepository.existsById(saleId)
                .flatMap(exists -> {
                    if (!exists) {
                        return Mono.error(new IllegalArgumentException("Sale no encontrado con ID: " + saleId));
                    }
                    return itemRepository.existsByCodCompuesto(itemCodCompuesto);
                })
                .flatMap(exists -> {
                    if (!exists) {
                        return Mono.error(new IllegalArgumentException("Item no encontrado con codCompuesto: " + itemCodCompuesto));
                    }
                    return Mono.just(true);
                });
    }

    /**
     * Valida que el Sale exista.
     */
    private Mono<Boolean> validateSaleExists(UUID saleId) {
        return saleRepository.existsById(saleId)
                .flatMap(exists -> {
                    if (!exists) {
                        return Mono.error(new IllegalArgumentException("Sale no encontrado con ID: " + saleId));
                    }
                    return Mono.just(true);
                });
    }

    /**
     * Valida que el Sale exista y tenga al menos un BienServicioCargado.
     */
    private Mono<Boolean> validateSaleHasBienServicioCargado(UUID saleId) {
        return validateSaleExists(saleId)
                .flatMap(valid -> saleRepository.hasBienServicioCargado(saleId))
                .flatMap(hasBienServicio -> {
                    if (!hasBienServicio) {
                        return Mono.error(new IllegalArgumentException("El Sale debe tener al menos un BienServicioCargado"));
                    }
                    return Mono.just(true);
                });
    }

    /**
     * Incrementa la cantidad de un BienServicioCargado existente.
     * Usa el repositorio específico para trabajar con objetos.
     */
    private Mono<UUID> incrementExistingBienServicioCargado(UUID saleId, String itemCodCompuesto, Double cantidadIncremento) {
        log.debug("Incrementando cantidad de BienServicioCargado existente para Item: {}", itemCodCompuesto);
        return saleRepository.incrementBienServicioCargadoCantidad(saleId, itemCodCompuesto, cantidadIncremento);
    }

    /**
     * Crea un nuevo BienServicioCargado usando repositorios específicos.
     * Maneja el caso cuando precioInicial es null (usa precioVentaPublico del Item).
     */
    private Mono<UUID> createNewBienServicioCargado(UUID saleId, String itemCodCompuesto, Double precioInicial, Double cantidad, String username) {
        log.debug("Creando nuevo BienServicioCargado para Item: {}", itemCodCompuesto);

        // Obtener precio inicial si es null
        Mono<Double> precioInicialMono = (precioInicial != null)
            ? Mono.just(precioInicial)
            : saleRepository.getItemPrecioVentaPublico(itemCodCompuesto);

        // Obtener descripción del Item
        Mono<String> descripcionMono = saleRepository.getItemDescripcion(itemCodCompuesto);

        return Mono.zip(precioInicialMono, descripcionMono)
                .flatMap(tuple -> {
                    Double finalPrecioInicial = tuple.getT1();
                    String descripcion = tuple.getT2();

                    // Crear BienServicioCargado usando repositorio específico
                    BienServicioCargado bienServicioCargado = new BienServicioCargado();
                    bienServicioCargado.setCargadoPor(username);
                    bienServicioCargado.setPrecioInicial(finalPrecioInicial);
                    bienServicioCargado.setCantidad(cantidad);
                    bienServicioCargado.setPrecioAcordado(finalPrecioInicial); // Inicialmente igual al precio inicial
                    bienServicioCargado.setMontoAcordado(finalPrecioInicial * cantidad);
                    bienServicioCargado.setDescripcionDelBienServicio(descripcion);
                    bienServicioCargado.setCreatedAt(Instant.now());

                    return bienServicioCargadoRepository.save(bienServicioCargado)
                            .flatMap(savedBsc -> {
                                // Crear relaciones usando consultas específicas
                                return saleRepository.createSaleBienServicioCargadoRelationship(saleId, savedBsc.getId())
                                        .flatMap(id -> saleRepository.createBienServicioCargadoItemRelationship(savedBsc.getId(), itemCodCompuesto))
                                        .thenReturn(savedBsc.getId());
                            });
                });
    }

    /**
     * Crea un CobroDineroProgramado con los datos especificados.
     * Método auxiliar para crear objetos usando datos de negocio.
     */
    private CobroDineroProgramado createCobroDineroProgramado(Double montoACobrar, String iniciadoPor) {
        CobroDineroProgramado cobro = new CobroDineroProgramado();
        cobro.setMontoACobrar(montoACobrar);
        cobro.setMontoRestante(montoACobrar); // Inicialmente es igual al monoACobrar
        cobro.setIniciadoPor(iniciadoPor);
        cobro.setFechaLimite(Instant.now().plusSeconds(30 * 60)); // 30 minutos desde ahora
        cobro.setEstaCobrado(false);
        cobro.setCreadoEl(Instant.now());
        return cobro;
    }

    /**
     * Crea un CobroDineroProgramado con límite de 1 semana para ventas a crédito y pedidos.
     * Método auxiliar para crear objetos usando datos de negocio.
     */
    private CobroDineroProgramado createCobroDineroProgramadoWithWeekLimit(Double montoACobrar, String iniciadoPor) {
        CobroDineroProgramado cobro = new CobroDineroProgramado();
        cobro.setMontoACobrar(montoACobrar);
        cobro.setMontoRestante(montoACobrar); // Inicialmente es igual al monoACobrar
        cobro.setIniciadoPor(iniciadoPor);
        cobro.setFechaLimite(Instant.now().plusSeconds(7 * 24 * 60 * 60)); // 1 semana desde ahora
        cobro.setEstaCobrado(false);
        cobro.setCreadoEl(Instant.now());
        return cobro;
    }

    /**
     * Valida la entrada del BienServicioDevuelto.
     * Verifica que cantidad y precioAcordadoDevolver estén presentes y cantidad > 0.
     */
    private Mono<Boolean> validateBienServicioDevueltoInput(BienServicioDevuelto bienServicioDevuelto) {
        if (bienServicioDevuelto.getCantidad() == null || bienServicioDevuelto.getPrecioAcordadoDevolver() == null) {
            return Mono.error(new IllegalArgumentException("cantidad y precioAcordadoDevolver son requeridos"));
        }
        if (bienServicioDevuelto.getCantidad() <= 0) {
            return Mono.error(new IllegalArgumentException("La cantidad debe ser mayor a cero"));
        }
        return Mono.just(true);
    }

    /**
     * Valida que la cantidad a devolver no sea mayor que la cantidad cargada.
     */
    private Mono<Boolean> validateCantidadDevolucion(Double cantidadDevolver, Double cantidadCargada) {
        if (cantidadDevolver > cantidadCargada) {
            return Mono.error(new IllegalArgumentException(
                "La cantidad a devolver (" + cantidadDevolver +
                ") no puede ser mayor que la cantidad cargada (" + cantidadCargada + ")"));
        }
        return Mono.just(true);
    }

    /**
     * Completa los campos del BienServicioDevuelto usando datos del BienServicioCargado.
     * Método auxiliar que modifica el objeto directamente (no reactivo).
     */
    private void completeBienServicioDevuelto(BienServicioDevuelto bienServicioDevuelto,
                                            BienServicioCargado bienServicioCargado,
                                            String username) {
        bienServicioDevuelto.setDevueltoPor(username);

        // Usar descripción del BienServicioCargado si no se proporcionó
        if (bienServicioDevuelto.getDescripcionDelBienServicio() == null ||
            bienServicioDevuelto.getDescripcionDelBienServicio().isEmpty()) {
            bienServicioDevuelto.setDescripcionDelBienServicio(bienServicioCargado.getDescripcionDelBienServicio());
        }

        // Calcular montoDevuelto = precioAcordadoDevolver * cantidad
        bienServicioDevuelto.setMontoDevuelto(
            bienServicioDevuelto.getPrecioAcordadoDevolver() * bienServicioDevuelto.getCantidad());

        bienServicioDevuelto.setIsDineroDevuelto(false);
        bienServicioDevuelto.setCreatedAt(Instant.now());
    }

    /**
     * Procesa las relaciones y modificaciones necesarias para la devolución.
     * Incluye crear relaciones, modificar/eliminar BienServicioCargado y crear DevolucionDinero.
     */
    private Mono<UUID> processDevolucionRelationshipsAndModifications(UUID saleId,
                                                                     UUID bienServicioCargadoId,
                                                                     BienServicioDevuelto savedBsd,
                                                                     BienServicioCargado bienServicioCargado,
                                                                     String username) {
        // Paso 1: Crear relación Sale-BienServicioDevuelto
        return saleRepository.createBienServicioDevueltoRelationship(saleId, savedBsd.getId())
                .flatMap(id -> {
                    // Paso 2: Crear relación BienServicioDevuelto-Item (usando el Item del BienServicioCargado)
                    return saleRepository.getItemCodCompuestoByBienServicioCargadoId(bienServicioCargadoId)
                            .flatMap(itemCodCompuesto ->
                                saleRepository.createBienServicioDevueltoItemRelationship(savedBsd.getId(), bienServicioCargadoId))
                            .flatMap(itemId -> {
                                // Paso 3: Modificar o eliminar BienServicioCargado
                                return modifyOrDeleteBienServicioCargado(bienServicioCargadoId, savedBsd, bienServicioCargado)
                                        .flatMap(bscId -> {
                                            // Paso 4: Crear DevolucionDinero
                                            return createAndSaveDevolucionDinero(saleId, savedBsd.getMontoDevuelto(), username);
                                        });
                            });
                });
    }

    /**
     * Modifica la cantidad del BienServicioCargado o lo elimina según la cantidad devuelta.
     */
    private Mono<UUID> modifyOrDeleteBienServicioCargado(UUID bienServicioCargadoId,
                                                        BienServicioDevuelto bienServicioDevuelto,
                                                        BienServicioCargado bienServicioCargado) {
        if (bienServicioDevuelto.getCantidad().equals(bienServicioCargado.getCantidad())) {
            // Cantidad devuelta = cantidad cargada → Eliminar BienServicioCargado
            log.debug("Eliminando BienServicioCargado completo, cantidad devuelta = cantidad cargada");
            return bienServicioCargadoRepository.deleteById(bienServicioCargadoId)
                    .thenReturn(bienServicioCargadoId);
        } else {
            // Cantidad devuelta < cantidad cargada → Actualizar cantidad
            log.debug("Reduciendo cantidad de BienServicioCargado");
            Double nuevaCantidad = bienServicioCargado.getCantidad() - bienServicioDevuelto.getCantidad();
            Double nuevoMontoAcordado = bienServicioCargado.getPrecioAcordado() * nuevaCantidad;

            // Actualizar usando repositorio específico
            bienServicioCargado.setCantidad(nuevaCantidad);
            bienServicioCargado.setMontoAcordado(nuevoMontoAcordado);

            return bienServicioCargadoRepository.save(bienServicioCargado)
                    .map(BienServicioCargado::getId);
        }
    }

    /**
     * Crea y guarda DevolucionDinero con relación al Sale.
     */
    private Mono<UUID> createAndSaveDevolucionDinero(UUID saleId, Double montoADevolver, String username) {
        DevolucionDinero devolucionDinero = new DevolucionDinero();
        devolucionDinero.setMontoADevolver(montoADevolver);
        devolucionDinero.setIniciadoPor(username);
        devolucionDinero.setCreadoEl(Instant.now());
        devolucionDinero.setEstaDevuelto(false);

        return devolucionDineroRepository.save(devolucionDinero)
                .flatMap(savedDd ->
                    saleRepository.createDevolucionDineroRelationship(saleId, savedDd.getId()));
    }

}
